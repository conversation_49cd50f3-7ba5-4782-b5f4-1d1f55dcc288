#!/usr/bin/env python3
"""
Flower Monitoring Dashboard Startup Script
"""
import os
import sys
from flower.command import FlowerCommand

if __name__ == "__main__":
    # Start Flower monitoring dashboard
    flower = FlowerCommand()
    flower.run_from_argv([
        'flower',
        '--broker=redis://localhost:6379/0',
        '--port=5555',
        '--basic_auth=admin:admin123'  # Change this in production
    ])
